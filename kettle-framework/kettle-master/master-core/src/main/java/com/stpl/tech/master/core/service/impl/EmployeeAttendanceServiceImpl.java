package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.cache.EmployeeCache;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.EmployeeAttendanceService;
import com.stpl.tech.master.data.dao.EmployeeAttendanceDao;
import com.stpl.tech.master.data.model.EmployeeAttendance;
import com.stpl.tech.master.data.model.ExcelRequestData;
import com.stpl.tech.master.domain.model.*;
import java.util.ArrayList;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;
import com.stpl.tech.master.core.external.excel.GenericExcelManagementService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service implementation for Employee Eligibility Mapping operations
 */
@Service
@Transactional
public class EmployeeAttendanceServiceImpl implements EmployeeAttendanceService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeAttendanceServiceImpl.class);

    @Autowired
    private EmployeeAttendanceDao employeeAttendanceDao;

    @Autowired
    private GenericExcelManagementService genericExcelManagementService;

    @Autowired
    private EmployeeCache empCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public EmployeeEligibilityMappingResponse saveEmployeeAttendanceMapping(EmployeeEligibilityMappingRequest request, String createdBy) throws DataUpdationException {
        // need improvment here what if i want to map to different units or rehions
        try {
            LOG.info("Saving employee eligibility mapping for empId: {}, type: {}, mappingType: {}, value: {}",
                    request.getEmpId(), request.getEligibilityType(), request.getMappingType(), request.getValue());
            List<EmployeeAttendance> existingMapping = employeeAttendanceDao.findByEmpIdValueAndType(request.getEmpId(),request.getValue(),request.getMappingType(),request.getEligibilityType());
            if(!existingMapping.isEmpty()){
                throw new DataUpdationException("Mapping already exists for this employee id and value");
            } else {

                // Create entity from request
                EmployeeAttendance mapping = new EmployeeAttendance();
                mapping.setEligibilityType(request.getEligibilityType());
                mapping.setEmpId(request.getEmpId());
                mapping.setMappingType(request.getMappingType());
                mapping.setValue(request.getValue());
                mapping.setCreatedBy(createdBy);
                mapping.setUpdatedBy(createdBy);
                mapping.setStatus(SystemStatus.ACTIVE);
                mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
                mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());

                // Save to database
                EmployeeAttendance savedMapping = employeeAttendanceDao.add(mapping);

                LOG.info("Successfully saved employee eligibility mapping with ID: {}", savedMapping.getId());

                // Convert to response
                return convertToResponse(savedMapping);
            }

        } catch (Exception e) {
            LOG.error("Error saving employee eligibility mapping", e);
            throw new DataUpdationException("Error saving employee eligibility mapping");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<EmployeeEligibilityMappingResponse> getEligibilityAttendanceMappingsByEmpId(String empId) {
        try {
            LOG.info("Getting mappings for empId: {}", empId);
            List<EmployeeAttendance> mappings = employeeAttendanceDao.findByEmpId(empId);
            return mappings.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Error getting mappings for empId: {}", empId, e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public View prepareEmployeeMappingExcel(List<Long> empIds) throws Exception {
        List<EmployeeAttendance> mappings = employeeAttendanceDao.findByEmpIdIn(empIds);

        List<String> headers = Arrays.asList(
                "EMPLOYEE_ID", "EMPLOYEE_NAME", "EMPLOYEE_CODE",
                "MAPPING_TYPE", "VALUE", "UNIT_NAME","ELIGIBILITY_TYPE", "STATUS"
        );

        List<Object[]> rows = new ArrayList<>();

        for (EmployeeAttendance mapping : mappings) {
            Long empId = Long.valueOf(mapping.getEmpId());
            EmployeeBasicDetail employeeDetail = empCache.getEmployee(empId.intValue());
            Map<Integer, Unit> units = masterDataCache.getUnits();

            String empName = employeeDetail != null ? employeeDetail.getName() : "";
            String empCode = employeeDetail != null ? employeeDetail.getEmployeeCode() : "";

            // Get unit name based on mapping type
            String unitName = "";
            if ("UNIT".equals(mapping.getMappingType().name())) {
                try {
                    Integer unitId = Integer.valueOf(mapping.getValue());
                    Unit unit = units.get(unitId);
                    unitName = unit != null ? unit.getName() : "";
                } catch (NumberFormatException e) {
                    LOG.warn("Invalid unit ID format: {}", mapping.getValue());
                    unitName = "";
                }
            } else {
                // For CITY and REGION mappings, the value is empty
                unitName = "";
            }

            rows.add(new Object[]{
                    empId,
                    empName,
                    empCode,
                    mapping.getMappingType(),
                    mapping.getValue(),
                    unitName,
                    mapping.getEligibilityType(),
                    mapping.getStatus()
            });
        }

        ExcelRequestData excelRequestData = new ExcelRequestData();
        excelRequestData.setFileName("Employee_Mappings");
        excelRequestData.setHeaderNames(headers);
        excelRequestData.setBody(rows);

        return genericExcelManagementService.downloadExcelFromRequestData(excelRequestData);
    }

    @Override
    @Transactional(readOnly = true)
    public View downloadBulkUploadTemplate() throws Exception {
        List<String> headers = Arrays.asList(
                "Eligibility_type", "Emp_id", "Mapping_type", "status", "value"
        );

        List<String[]> rows = Arrays.asList(
                new String[]{"ATTENDANCE", "EMP001", "UNIT", "ACTIVE", "100000"},
                new String[]{"APPROVAL", "EMP002", "CITY", "ACTIVE", "Delhi"},
                new String[]{"ATTENDANCE", "EMP003", "REGION", "ACTIVE", "North"}
        );

        List<Object[]> objectRows = rows.stream()
                .map(arr -> Arrays.copyOf(arr, arr.length, Object[].class))
                .collect(Collectors.toList());

        ExcelRequestData excelRequestData = new ExcelRequestData();
        excelRequestData.setFileName("Employee_Eligibility_Mapping_Template");
        excelRequestData.setHeaderNames(headers);
        excelRequestData.setBody(objectRows);

        return genericExcelManagementService.downloadExcelFromRequestData(excelRequestData);
    }

    @Override
    @Transactional(readOnly = true)
    public BulkUploadResponse validateBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData) throws Exception {
        LOG.info("Validating bulk upload with {} records", bulkData.size());

        BulkUploadResponse response = new BulkUploadResponse();
        response.setTotalRecords(bulkData.size());
        response.setValidationErrors(new ArrayList<>());

        List<EmployeeEligibilityMappingRequest> validRecords = new ArrayList<>();
        int rowNumber = 2; // Starting from row 2 (after header)

        // Validate each record
        for (EmployeeEligibilityMappingRequest request : bulkData) {
            List<BulkUploadResponse.BulkUploadValidationError> recordErrors = validateBulkUploadRecord(request, rowNumber);

            if (recordErrors.isEmpty()) {
                validRecords.add(request);
            } else {
                response.getValidationErrors().addAll(recordErrors);
            }
            rowNumber++;
        }

        response.setValidRecords(validRecords.size());
        response.setInvalidRecords(bulkData.size() - validRecords.size());
        response.setSuccessfullyProcessed(0); // No processing done yet
        response.setSuccess(response.getValidationErrors().isEmpty());
        response.setMessage(String.format("Validation completed: %d valid records, %d invalid records found.",
                validRecords.size(), response.getValidationErrors().size()));

        LOG.info("Bulk validation completed: {} valid, {} invalid", validRecords.size(), response.getValidationErrors().size());
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public BulkUploadResponse processBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData, String createdBy) throws Exception {
        LOG.info("Processing bulk upload with {} records", bulkData.size());

        BulkUploadResponse response = new BulkUploadResponse();
        response.setTotalRecords(bulkData.size());
        response.setValidationErrors(new ArrayList<>());

        List<EmployeeEligibilityMappingRequest> validRecords = new ArrayList<>();
        int rowNumber = 2; // Starting from row 2 (after header)

        // Validate each record
        for (EmployeeEligibilityMappingRequest request : bulkData) {
            List<BulkUploadResponse.BulkUploadValidationError> recordErrors = validateBulkUploadRecord(request, rowNumber);

            if (recordErrors.isEmpty()) {
                validRecords.add(request);
            } else {
                response.getValidationErrors().addAll(recordErrors);
            }
            rowNumber++;
        }

        response.setValidRecords(validRecords.size());
        response.setInvalidRecords(bulkData.size() - validRecords.size());

        // Process valid records
        int successCount = 0;
        for (EmployeeEligibilityMappingRequest validRequest : validRecords) {
            try {
                saveEmployeeAttendanceMapping(validRequest, createdBy);
                successCount++;
            } catch (Exception e) {
                LOG.error("Error saving mapping for empId: {}", validRequest.getEmpId(), e);
                BulkUploadResponse.BulkUploadValidationError error = new BulkUploadResponse.BulkUploadValidationError();
                error.setRowNumber(rowNumber);
                error.setEmpId(validRequest.getEmpId());
                error.setField("general");
                error.setErrorMessage("Failed to save: " + e.getMessage());
                response.getValidationErrors().add(error);
            }
        }

        response.setSuccessfullyProcessed(successCount);
        response.setSuccess(successCount > 0);
        response.setMessage(String.format("Processed %d out of %d records successfully. %d validation errors found.",
                successCount, bulkData.size(), response.getValidationErrors().size()));

        LOG.info("Bulk upload completed: {} successful, {} errors", successCount, response.getValidationErrors().size());
        return response;
    }

    private List<BulkUploadResponse.BulkUploadValidationError> validateBulkUploadRecord(EmployeeEligibilityMappingRequest request, int rowNumber) {
        List<BulkUploadResponse.BulkUploadValidationError> errors = new ArrayList<>();

        // Validate eligibility type
        if (request.getEligibilityType() == null) {
            errors.add(createValidationError(rowNumber, request.getEmpId(), "eligibilityType", "Eligibility type is required", ""));
        }

        // Validate employee ID
        if (request.getEmpId() == null || request.getEmpId().trim().isEmpty()) {
            errors.add(createValidationError(rowNumber, request.getEmpId(), "empId", "Employee ID is required", request.getEmpId()));
        }

        // Validate mapping type
        if (request.getMappingType() == null) {
            errors.add(createValidationError(rowNumber, request.getEmpId(), "mappingType", "Mapping type is required", ""));
        }

        // Validate value
        if (request.getValue() == null || request.getValue().trim().isEmpty()) {
            errors.add(createValidationError(rowNumber, request.getEmpId(), "value", "Value is required", request.getValue()));
        }

        EmployeeAttendance existingMapping = employeeAttendanceDao.findByEmpIdValueAndType(request.getEmpId(),request.getValue(),request.getMappingType(),request.getEligibilityType()).stream().findFirst().orElse(null);
        if (existingMapping != null) {
            errors.add(createValidationError(rowNumber, request.getEmpId(), "value", "Mapping already exists", request.getValue()));
        }

        return errors;
    }

    private BulkUploadResponse.BulkUploadValidationError createValidationError(int rowNumber, String empId, String field, String message, String originalValue) {
        BulkUploadResponse.BulkUploadValidationError error = new BulkUploadResponse.BulkUploadValidationError();
        error.setRowNumber(rowNumber);
        error.setEmpId(empId);
        error.setField(field);
        error.setErrorMessage(message);
        error.setOriginalValue(originalValue);
        return error;
    }



    /**
     * Convert entity to response DTO
     * @param mapping entity
     * @return response DTO
     */
    private EmployeeEligibilityMappingResponse convertToResponse(EmployeeAttendance mapping) {
        EmployeeEligibilityMappingResponse response = new EmployeeEligibilityMappingResponse();
        response.setId(mapping.getId());
        response.setCreatedAt(mapping.getCreatedAt());
        response.setCreatedBy(mapping.getCreatedBy());
        response.setEligibilityType(mapping.getEligibilityType());
        response.setEmpId(mapping.getEmpId());
        response.setMappingType(mapping.getMappingType());
        response.setStatus(mapping.getStatus());
        response.setUpdatedAt(mapping.getUpdatedAt());
        response.setUpdatedBy(mapping.getUpdatedBy());
        response.setValue(mapping.getValue());
        return response;
    }
}
